# PostHog Analytics Setup

## Overview

PostHog analytics has been successfully integrated into the Trade Sensei Chat application with the configuration you provided.

## Configuration Details

### PostHog Key
- **Key**: `phc_svkkrbxrYi4ckp5G1CmJuVUetTARRYLSFPd4Mbtw8Mz`
- **API Host**: `https://us.i.posthog.com`
- **Person Profiles**: `identified_only`
- **Defaults**: `2025-05-24`

### Integration Methods

The PostHog integration uses two loading methods for maximum reliability:

1. **HTML Script Tag** (in `index.html`)
   - Loads PostHog directly in the browser
   - Provides immediate availability
   - Uses the exact script you provided

2. **JavaScript Module** (in `src/utils/analytics.ts`)
   - Programmatic initialization
   - Additional configuration options
   - Custom event tracking functions

## Environment Variables

The following environment variables are configured in `.env`:

```bash
# PostHog Configuration
POSTHOG_KEY=phc_svkkrbxrYi4ckp5G1CmJuVUetTARRYLSFPd4Mbtw8Mz
POSTHOG_HOST=https://us.i.posthog.com
VITE_POSTHOG_KEY=phc_svkkrbxrYi4ckp5G1CmJuVUetTARRYLSFPd4Mbtw8Mz
VITE_POSTHOG_HOST=https://us.i.posthog.com
```

## Testing PostHog

### Development Mode Testing

In development mode, you can test PostHog functionality using the browser console:

1. Open your browser's developer tools (F12)
2. Navigate to the Console tab
3. Run the test function:

```javascript
testPostHog()
```

This will:
- Check if PostHog is properly loaded
- Send a test event to PostHog
- Display confirmation messages in the console

### Manual Testing

You can also manually send events to PostHog:

```javascript
// Using the window object (from HTML script)
window.posthog.capture('custom_event', {
  property1: 'value1',
  property2: 'value2'
});

// Using the imported module
import { posthog } from './utils/analytics';
posthog.capture('custom_event', { test: true });
```

## Automatic Event Tracking

The application automatically tracks several events:

1. **App Initialization Performance**
   - Event: `app_init_performance`
   - Includes: initialization time, user agent, connection type

2. **App Errors**
   - Event: `app_error`
   - Includes: error message, stack trace, component stack

3. **AI/LLM Events**
   - Event: `$ai_generation`
   - Includes: model, provider, input/output tokens, latency

## Files Modified

1. **`src/utils/analytics.ts`**
   - Updated PostHog initialization with your specific configuration
   - Added test function for development
   - Enhanced error handling and logging

2. **`index.html`**
   - Added PostHog script tag with your exact configuration
   - Provides immediate PostHog availability

3. **`src/main.tsx`**
   - Added development mode test function to window object
   - Enhanced initialization logging

4. **`.env`**
   - Added VITE-prefixed environment variables for client-side access
   - Organized PostHog configuration section

## Verification

To verify PostHog is working correctly:

1. **Check Browser Console**: Look for PostHog initialization messages
2. **Run Test Function**: Use `testPostHog()` in the console
3. **Check PostHog Dashboard**: Events should appear in your PostHog project
4. **Network Tab**: Verify requests are being sent to `https://us.i.posthog.com`

## Next Steps

1. **Custom Events**: Add specific business logic events throughout your application
2. **User Identification**: Implement user identification when users log in
3. **Feature Flags**: Utilize PostHog's feature flag capabilities
4. **A/B Testing**: Set up experiments using PostHog's testing features

## Support

If you encounter any issues:

1. Check the browser console for error messages
2. Verify environment variables are properly set
3. Ensure the PostHog key is valid and active
4. Check network connectivity to PostHog servers

The integration is now complete and ready for use!
