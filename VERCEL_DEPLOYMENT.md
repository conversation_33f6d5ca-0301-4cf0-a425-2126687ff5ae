# Vercel Deployment Guide

This guide explains how to deploy your Trade Sensei Chat application (Vite + Express) to Vercel.

## Architecture Overview

The deployment configuration converts your Express server into serverless functions while maintaining the Vite frontend as static files:

- **Frontend**: Vite build → Static files served by Vercel
- **Backend**: Express routes → Individual serverless functions in `/api` directory
- **Routing**: Configured in `vercel.json` to route API calls to functions and SPA routes to `index.html`

## Files Structure

```
├── vercel.json              # Vercel configuration
├── api/                     # Serverless functions (replaces Express server)
│   ├── package.json         # Dependencies for serverless functions
│   └── whop/
│       ├── current-user.js  # GET /api/whop/current-user
│       ├── check-access.js  # POST /api/whop/check-access
│       ├── test-connection.js # GET /api/whop/test-connection
│       ├── health.js        # GET /api/whop/health
│       └── debug-headers.js # GET /api/whop/debug-headers
├── dist/                    # Vite build output (static files)
└── scripts/deploy-vercel.js # Deployment helper script
```

## Environment Variables

Set these environment variables in your Vercel project dashboard:

### Required Variables
- `VITE_WHOP_APP_ID` - Your Whop app ID
- `WHOP_API_KEY` - Your Whop API key (server-side only)
- `VITE_WHOP_AGENT_USER_ID` - Whop agent user ID
- `VITE_WHOP_COMPANY_ID` - Whop company ID

### Setting Environment Variables

#### Via Vercel Dashboard:
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Go to **Settings** → **Environment Variables**
4. Add each variable with appropriate values

#### Via Vercel CLI:
```bash
vercel env add VITE_WHOP_APP_ID
vercel env add WHOP_API_KEY
vercel env add VITE_WHOP_AGENT_USER_ID
vercel env add VITE_WHOP_COMPANY_ID
```

## Deployment Steps

### 1. Prepare for Deployment
```bash
# Run the deployment preparation script
npm run deploy:vercel
```

This script will:
- Verify `vercel.json` exists
- Check API functions are present
- Build the project
- Show deployment checklist

### 2. Deploy to Vercel

#### Option A: Automatic Deployment (Recommended)
1. Connect your GitHub repository to Vercel
2. Push changes to your main branch
3. Vercel will automatically deploy

#### Option B: Manual Deployment
```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Deploy to production
vercel --prod
```

### 3. Verify Deployment

After deployment, test these endpoints:
- `https://your-domain.vercel.app/` - Main application
- `https://your-domain.vercel.app/api/whop/health` - Health check
- `https://your-domain.vercel.app/api/whop/test-connection` - Whop connection test

## Configuration Details

### vercel.json Explanation

```json
{
  "version": 2,
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "functions": {
    // Serverless function configurations
  },
  "routes": [
    // API routes → serverless functions
    // Static assets → direct serving
    // SPA routes → index.html
  ],
  "env": {
    // Environment variable references
  }
}
```

### Key Features:
- **Serverless Functions**: Each Express route becomes a separate function
- **CORS Handling**: Built into each function
- **Static File Serving**: Vite build served directly
- **SPA Routing**: All non-API routes serve `index.html`

## Troubleshooting

### Common Issues:

1. **Environment Variables Not Working**
   - Ensure variables are set in Vercel dashboard
   - Check variable names match exactly
   - Redeploy after adding variables

2. **API Functions Not Working**
   - Check function logs in Vercel dashboard
   - Verify `@whop/api` dependency is available
   - Ensure CORS headers are set

3. **Build Failures**
   - Check build logs in Vercel dashboard
   - Ensure all dependencies are in `package.json`
   - Verify build command works locally

4. **Routing Issues**
   - Check `vercel.json` route configuration
   - Ensure API routes come before catch-all route
   - Test routes individually

### Debugging:
- Use `/api/whop/debug-headers` to check request headers
- Check Vercel function logs in dashboard
- Use `vercel logs` CLI command

## Migration from Express Server

The serverless functions replace your Express server (`server/whop-api.cjs`). Key differences:

- **No persistent server**: Each function runs independently
- **Cold starts**: Functions may have slight delay on first request
- **Stateless**: No shared memory between requests
- **CORS**: Must be handled in each function

## Performance Considerations

- **Cold Starts**: First request to a function may be slower
- **Function Size**: Keep functions small for faster cold starts
- **Caching**: Vercel automatically caches static assets
- **Edge Network**: Functions run on Vercel's global edge network

## Next Steps

After successful deployment:
1. Set up custom domain (if needed)
2. Configure monitoring and alerts
3. Set up CI/CD pipeline
4. Monitor function performance and costs
