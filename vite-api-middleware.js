import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Middleware to handle API routes in development
export function apiMiddleware() {
  return {
    name: 'api-middleware',
    configureServer(server) {
      server.middlewares.use('/api', async (req, res, next) => {
        // Only handle /api/whop routes
        if (!req.url.startsWith('/whop/')) {
          return next();
        }

        try {
          // Extract the route path (e.g., '/whop/current-user')
          const routePath = req.url.split('?')[0]; // Remove query params
          const apiPath = join(__dirname, 'api', routePath + '.js');

          // Check if the API file exists
          if (!fs.existsSync(apiPath)) {
            res.statusCode = 404;
            res.end(JSON.stringify({ error: 'API route not found' }));
            return;
          }

          // Import and execute the API handler
          const { default: handler } = await import(apiPath + '?t=' + Date.now());
          
          // Create a mock response object with Vercel-like methods
          const mockRes = {
            statusCode: 200,
            headers: {},
            setHeader(name, value) {
              this.headers[name] = value;
            },
            status(code) {
              this.statusCode = code;
              return this;
            },
            json(data) {
              this.setHeader('Content-Type', 'application/json');
              res.statusCode = this.statusCode;
              Object.entries(this.headers).forEach(([key, value]) => {
                res.setHeader(key, value);
              });
              res.end(JSON.stringify(data));
            },
            end(data) {
              res.statusCode = this.statusCode;
              Object.entries(this.headers).forEach(([key, value]) => {
                res.setHeader(key, value);
              });
              res.end(data);
            }
          };

          // Execute the handler
          await handler(req, mockRes);
        } catch (error) {
          console.error('API Error:', error);
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({ 
            error: 'Internal Server Error', 
            message: error.message 
          }));
        }
      });
    }
  };
}
