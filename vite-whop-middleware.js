/**
 * Vite middleware for handling Whop proxy requests
 * This middleware intercepts requests to /api/whop-proxy and forwards them to the intermediary server
 */

// Use production URL for both development and production
const INTERMEDIARY_SERVER_URL = process.env.VITE_WHOP_INTERMEDIARY_URL || 'https://whop-intermediary-server.vercel.app';

export function whopProxyMiddleware() {
  return {
    name: 'whop-proxy',
    configureServer(server) {
      server.middlewares.use('/api/whop-proxy', async (req, res, next) => {
        try {
          console.log('🔄 Whop Proxy Middleware: Handling request');
          console.log('📍 Method:', req.method);
          console.log('📍 URL:', req.url);
          console.log('📍 Headers:', Object.keys(req.headers));
          console.log('🔍 Whop token present:', !!req.headers['x-whop-user-token']);
          console.log('🔍 User agent:', req.headers['user-agent']);

          // Handle CORS preflight requests
          if (req.method === 'OPTIONS') {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-whop-user-token');
            res.setHeader('Access-Control-Allow-Credentials', 'true');
            res.statusCode = 200;
            res.end();
            return;
          }

          // Parse query parameters
          const url = new URL(req.url, `http://${req.headers.host}`);
          const endpoint = url.searchParams.get('endpoint');
          
          if (!endpoint) {
            res.statusCode = 400;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              success: false,
              error: 'Missing endpoint parameter'
            }));
            return;
          }

          // Construct the full URL to the intermediary server
          const targetUrl = `${INTERMEDIARY_SERVER_URL}/api/${endpoint}`;
          
          // Prepare headers to forward
          const forwardHeaders = {
            'Content-Type': 'application/json',
          };

          // Forward the Whop user token if present
          if (req.headers['x-whop-user-token']) {
            forwardHeaders['x-whop-user-token'] = req.headers['x-whop-user-token'];
            console.log('✅ Forwarding Whop user token to intermediary server');
            console.log('🔍 Token preview:', req.headers['x-whop-user-token'].substring(0, 20) + '...');
          } else {
            console.log('⚠️ No Whop user token found in request headers');
            console.log('📋 Available headers:', Object.keys(req.headers));
          }

          // Also forward other Whop-related headers that might be present
          const whopHeaders = [
            'x-whop-user-token',
            'x-whop-company-id',
            'x-whop-app-id',
            'x-whop-experience-id'
          ];

          whopHeaders.forEach(headerName => {
            if (req.headers[headerName]) {
              forwardHeaders[headerName] = req.headers[headerName];
              console.log(`✅ Forwarding ${headerName}`);
            }
          });

          // Forward other relevant headers
          if (req.headers['authorization']) {
            forwardHeaders['authorization'] = req.headers['authorization'];
          }

          // Prepare the request options
          const requestOptions = {
            method: req.method,
            headers: forwardHeaders,
          };

          // Handle request body for POST/PUT requests
          if (req.method === 'POST' || req.method === 'PUT') {
            let body = '';
            req.on('data', chunk => {
              body += chunk.toString();
            });
            
            await new Promise(resolve => {
              req.on('end', resolve);
            });
            
            if (body) {
              requestOptions.body = body;
            }
          }

          console.log('🚀 Forwarding to:', targetUrl);
          console.log('📤 Request options:', {
            method: requestOptions.method,
            headers: Object.keys(requestOptions.headers),
            hasBody: !!requestOptions.body
          });

          // Make the request to the intermediary server
          const response = await fetch(targetUrl, requestOptions);
          const data = await response.text();

          console.log('📥 Response from intermediary server:', {
            status: response.status,
            contentType: response.headers.get('content-type')
          });

          // Forward the response
          res.statusCode = response.status;
          res.setHeader('Content-Type', response.headers.get('content-type') || 'application/json');
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.end(data);

        } catch (error) {
          console.error('❌ Error in Whop proxy middleware:', error);
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            success: false,
            error: 'Proxy error',
            message: error.message
          }));
        }
      });
    }
  };
}
