import React from 'react';
import { useParams } from 'react-router-dom';
import WhopExperiencePage from '@/components/whop/WhopExperiencePage';
import WhopAuthGuard from '@/components/whop/WhopAuthGuard';
import WhopPageLayout from '@/components/whop/WhopPageLayout';
import Home from './Home';
import Trading from './Trading';
import { useWhopUser } from '@/contexts/WhopContext';

const WhopExperience: React.FC = () => {
  const { experienceId } = useParams<{ experienceId: string }>();
  const { isWhopUser } = useWhopUser();

  console.log('🎯 Whop Experience Page:', { experienceId, isWhopUser });

  // Determine if this is a trading experience
  const isTradingExperience = experienceId === 'exp_ThljdpAF70d4Af';

  // For trading experiences, render the Trading component without layout
  if (isTradingExperience) {
    return (
      <WhopExperiencePage>
        <WhopAuthGuard requireAccess={true}>
          {/*
            Trading experience renders the full-screen Trading component
            without the WhopPageLayout to maintain the standalone trading interface
          */}
          <Trading />
        </WhopAuthGuard>
      </WhopExperiencePage>
    );
  }

  // For OSIS experiences, render the Home component with layout
  return (
    <WhopExperiencePage>
      <WhopAuthGuard requireAccess={true}>
        <WhopPageLayout>
          {/*
            For OSIS Whop users, we render the main Home component
            but within the Whop experience context with mobile-style navigation.
            This ensures they get the same functionality as regular users
            but with a mobile-optimized interface.
          */}
          <Home />
        </WhopPageLayout>
      </WhopAuthGuard>
    </WhopExperiencePage>
  );
};

export default WhopExperience;
