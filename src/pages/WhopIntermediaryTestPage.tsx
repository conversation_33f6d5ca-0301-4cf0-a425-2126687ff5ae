import React from 'react';
import { WhopIntermediaryTest } from '@/components/WhopIntermediaryTest';

/**
 * Standalone test page for the Whop Intermediary Server
 * Access this at /whop-test in your main app
 */
const WhopIntermediaryTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Whop Intermediary Server Test
          </h1>
          <p className="text-gray-600">
            Test the connection and functionality of the Whop intermediary server
          </p>
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> This page tests the intermediary server running at{' '}
              <code className="bg-blue-100 px-1 rounded">
                {import.meta.env.VITE_WHOP_INTERMEDIARY_URL || 'http://localhost:3000'}
              </code>
            </p>
          </div>
        </div>
        
        <WhopIntermediaryTest />
        
        <div className="mt-8 p-6 bg-white rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Setup Instructions</h2>
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-medium text-gray-900">1. Start the Intermediary Server</h3>
              <code className="block bg-gray-100 p-2 rounded mt-1">
                cd whop-intermediary-server && npm run dev
              </code>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900">2. Configure Environment Variables</h3>
              <p className="text-gray-600 mt-1">
                Make sure your <code>whop-intermediary-server/.env.local</code> file has:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1 text-gray-600">
                <li>WHOP_APP_ID</li>
                <li>WHOP_API_KEY</li>
                <li>WHOP_AGENT_USER_ID</li>
                <li>WHOP_COMPANY_ID</li>
                <li>SUPABASE_URL</li>
                <li>SUPABASE_SERVICE_ROLE_KEY</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900">3. Test the Connection</h3>
              <p className="text-gray-600 mt-1">
                Use the buttons above to test different aspects of the intermediary server.
                Start with "Health Check" and "Test Connection" to verify basic functionality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhopIntermediaryTestPage;
