import React from 'react';
import { useParams, useLocation } from 'react-router-dom';
import TradingChart from '@/components/TradingChart';
import { useNavigate } from 'react-router-dom';


interface ScanContext {
  result: {
    symbol: string;
    signal: string;
    confidence: number;
    price?: number;
    change?: number;
    percentChange?: number;
  };
  agent: {
    id: string;
    name: string;
  };
  market: {
    value: string;
    label: string;
  };
  scanTimestamp: string; 
}

const Screener: React.FC = () => {
  const { symbol } = useParams<{ symbol?: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const displaySymbol = symbol || 'TSLA';
  const scanContext = location.state?.scanContext as ScanContext | undefined;





  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const scanTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - scanTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="h-full bg-[#0A0A0A] overflow-hidden flex flex-col">
      {/* Agent Scan Context Header */}
      {scanContext && (
        <div className="bg-[#141414] border-b border-white/[0.08] px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <button
                onClick={() => navigate('/agent-scanner')}
                className="text-white/70 hover:text-white text-sm font-medium transition-colors duration-200 px-3 py-1.5 rounded-lg hover:bg-white/5"
              >
                ← Back to Scanner
              </button>

              <div className="h-5 w-px bg-white/20" />

              <div className="flex items-center gap-4">
                <div className="flex flex-col">
                  <div className="flex items-center gap-3">
                    <h2 className="text-white font-semibold text-lg tracking-tight">{displaySymbol}</h2>

                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      scanContext.result.confidence >= 80
                        ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                        : scanContext.result.confidence >= 60
                        ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                        : 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                    }`}>
                      {scanContext.result.confidence}% Confidence
                    </div>

                    <div className={`px-3 py-1 rounded-full text-xs font-medium border ${
                      scanContext.result.signal === 'bullish'
                        ? 'bg-green-500/20 text-green-300 border-green-500/30'
                        : 'bg-red-500/20 text-red-300 border-red-500/30'
                    }`}>
                      {scanContext.result.signal.toUpperCase()}
                    </div>
                  </div>

                  <div className="flex items-center gap-3 text-xs text-white/60 mt-2">
                    <span>Agent: {scanContext.agent.name}</span>
                    <span className="text-white/30">•</span>
                    <span>Market: {scanContext.market.label}</span>
                    <span className="text-white/30">•</span>
                    <span>Scanned {formatTimeAgo(scanContext.scanTimestamp)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Price info if available */}
            {scanContext.result.price && (
              <div className="text-right">
                <div className="text-2xl font-bold text-white tracking-tight">
                  ${scanContext.result.price.toFixed(2)}
                </div>
                {scanContext.result.change !== undefined && (
                  <div className={`text-sm font-medium mt-1 ${
                    scanContext.result.change >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {scanContext.result.change >= 0 ? '+' : ''}${scanContext.result.change.toFixed(2)}
                    {scanContext.result.percentChange !== undefined && (
                      <span className="ml-1">
                        ({scanContext.result.percentChange >= 0 ? '+' : ''}{scanContext.result.percentChange.toFixed(2)}%)
                      </span>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trading Chart */}
      <div className="flex-1 w-full">
        <TradingChart symbol={displaySymbol} />
      </div>
    </div>
  );
};

export default Screener;
