import React, { useState, useCallback } from 'react';
import { handleRRDrag } from './RiskRewardTool';

export interface DragState {
  isDragging: boolean;
  dragType: 'tool' | 'handle' | null;
  drawingIndex: number;
  pointIndex?: number;
  handleType?: string; // For RR tools: 'entry', 'stop', 'target', 'resize'
  startPosition: { x: number; y: number } | null;
  currentPosition: { x: number; y: number } | null;
}

export interface DraggableToolSystemProps {
  drawings: any[];
  onUpdateDrawing: (index: number, updatedDrawing: any) => void;
  chartRef: React.RefObject<any>;
  chartBounds?: any;
}

export const useDraggableTools = ({
  drawings,
  onUpdateDrawing,
  chartRef,
  chartBounds
}: DraggableToolSystemProps) => {
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragType: null,
    drawingIndex: -1,
    handleType: undefined,
    startPosition: null,
    currentPosition: null
  });

  // Convert pixel coordinates to chart coordinates
  const pixelToChartCoords = useCallback((pixelX: number, pixelY: number) => {
    if (!chartRef.current) return null;

    try {
      const chart = chartRef.current.getEchartsInstance();
      const convertedPoint = chart.convertFromPixel('grid', [pixelX, pixelY]);

      if (!convertedPoint || convertedPoint.length < 2) return null;

      // Handle time conversion properly
      const timeValue = convertedPoint[0];
      const time = typeof timeValue === 'number' ? new Date(timeValue).toISOString() : timeValue;

      return {
        time: time,
        price: convertedPoint[1]
      };
    } catch (error) {
      console.error('Error converting pixel to chart coordinates:', error);
      return null;
    }
  }, [chartRef]);

  // Convert chart coordinates to pixel coordinates
  const chartToPixelCoords = useCallback((time: string, price: number) => {
    if (!chartRef.current) return null;

    try {
      const chart = chartRef.current.getEchartsInstance();
      const timeValue = new Date(time).getTime();
      const pixelPoint = chart.convertToPixel('grid', [timeValue, price]);
      
      if (!pixelPoint || pixelPoint.length < 2) return null;

      return {
        x: pixelPoint[0],
        y: pixelPoint[1]
      };
    } catch (error) {
      console.error('Error converting chart to pixel coordinates:', error);
      return null;
    }
  }, [chartRef]);

  // Start dragging a tool or handle
  const startDrag = useCallback((
    event: MouseEvent,
    drawingIndex: number,
    dragType: 'tool' | 'handle',
    pointIndex?: number,
    handleType?: string
  ) => {
    console.log('🚀 Starting drag:', { drawingIndex, dragType, handleType });

    // IMMEDIATELY prevent all default behaviors
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    const rect = chartRef.current?.getEchartsInstance()?.getDom()?.getBoundingClientRect();
    if (!rect) {
      console.log('❌ No chart rect found');
      return;
    }

    const startPos = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    console.log('📍 Start position:', startPos);

    setDragState({
      isDragging: true,
      dragType,
      drawingIndex,
      pointIndex,
      handleType,
      startPosition: startPos,
      currentPosition: startPos
    });

    // COMPLETELY disable chart interactions during drag
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance();
      if (chartInstance) {
        // Disable all chart interactions
        chartInstance.setOption({
          dataZoom: [{
            type: 'inside',
            xAxisIndex: [0],
            disabled: true, // Completely disable
            zoomOnMouseWheel: false,
            moveOnMouseMove: false,
            preventDefaultMouseMove: true
          }]
        });
      }
    }

    // Add global mouse event listeners with passive: false to ensure we can prevent defaults
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: false });

    // Also add pointer events for better touch support
    document.addEventListener('pointermove', handleMouseMove, { passive: false });
    document.addEventListener('pointerup', handleMouseUp, { passive: false });
  }, [chartRef]);

  // Handle mouse move during drag
  const handleMouseMove = useCallback((event: MouseEvent | PointerEvent) => {
    if (!dragState.isDragging || !dragState.startPosition) return;

    // Prevent all default behaviors during drag
    event.preventDefault();
    event.stopPropagation();

    const rect = chartRef.current?.getEchartsInstance()?.getDom()?.getBoundingClientRect();
    if (!rect) return;

    const currentPos = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    setDragState(prev => ({
      ...prev,
      currentPosition: currentPos
    }));

    // Update drawing in real-time
    updateDrawingPosition(currentPos);
  }, [dragState, chartRef]);

  // Handle mouse up to end drag
  const handleMouseUp = useCallback((event: MouseEvent | PointerEvent) => {
    if (!dragState.isDragging) return;

    console.log('🏁 Ending drag');

    // Prevent default behaviors
    event.preventDefault();
    event.stopPropagation();

    // Remove ALL event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('pointermove', handleMouseMove);
    document.removeEventListener('pointerup', handleMouseUp);

    // Final position update
    const rect = chartRef.current?.getEchartsInstance()?.getDom()?.getBoundingClientRect();
    if (rect) {
      const finalPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };
      updateDrawingPosition(finalPos, true);
    }

    // Re-enable chart panning after drag ends - restore full dataZoom configuration
    if (chartRef.current) {
      const currentOption = chartRef.current.getOption();
      const currentZoom = currentOption?.dataZoom?.[0];

      // Small delay to ensure drag is completely finished
      setTimeout(() => {
        if (chartRef.current) {
          console.log('🔄 Re-enabling chart interactions');
          // Restore the complete dataZoom configuration to match the main chart
          chartRef.current.setOption({
            dataZoom: [
              {
                type: 'inside',
                xAxisIndex: [0],
                start: currentZoom?.start || 85,
                end: currentZoom?.end || 100,
                minSpan: 1,
                maxSpan: 100,
                zoomOnMouseWheel: true,
                moveOnMouseMove: true,
                preventDefaultMouseMove: false,
                throttle: 0, // No throttling for TradingView-style response
                zoomLock: false,
                realtime: true,
                filterMode: 'none',
                disabled: false // Re-enable chart interactions
              },
              // Also restore volume chart zoom if it exists
              ...(currentOption?.dataZoom?.slice(1) || [])
            ]
          });
        }
      }, 100); // Slightly longer delay to prevent conflicts
    }

    setDragState({
      isDragging: false,
      dragType: null,
      drawingIndex: -1,
      handleType: undefined,
      startPosition: null,
      currentPosition: null
    });
  }, [dragState, chartRef]);

  // Update drawing position based on drag
  const updateDrawingPosition = useCallback((
    currentPos: { x: number; y: number },
    isFinal: boolean = false
  ) => {
    if (!dragState.startPosition || dragState.drawingIndex < 0) return;

    const drawing = drawings[dragState.drawingIndex];
    if (!drawing) return;

    // Handle RR tools specifically
    if (drawing.type === 'rr') {
      const newCoords = pixelToChartCoords(currentPos.x, currentPos.y);
      if (!newCoords) {
        console.log('❌ Failed to convert pixel coordinates for RR tool');
        return;
      }

      // Get the handle type from the series name or stored data
      const handleType = dragState.handleType || 'entry'; // Default to entry if not specified

      console.log('🎯 RR Tool drag update:', {
        handleType,
        newPrice: newCoords.price,
        newTime: newCoords.time,
        currentPos
      });

      // For resize handles, ensure we're providing both price and time coordinates
      let timeCoord = newCoords.time;

      // For horizontal resize handles, prioritize time coordinate conversion
      if (handleType === 'resize_left' || handleType === 'resize_right' ||
          handleType.includes('corner')) {
        // Ensure time coordinate is properly converted
        timeCoord = newCoords.time;
      }

      const updatedDrawing = handleRRDrag(drawing, handleType, newCoords.price, timeCoord);
      if (updatedDrawing) {
        console.log('✅ RR Tool updated successfully');
        onUpdateDrawing(dragState.drawingIndex, updatedDrawing);
      } else {
        console.log('❌ RR Tool update failed');
      }
      return;
    }

    // Handle regular drawing tools
    const deltaX = currentPos.x - dragState.startPosition.x;
    const deltaY = currentPos.y - dragState.startPosition.y;

    if (dragState.dragType === 'handle' && dragState.pointIndex !== undefined) {
      // Update specific point
      const newCoords = pixelToChartCoords(currentPos.x, currentPos.y);
      if (!newCoords) return;

      const updatedDrawing = {
        ...drawing,
        points: drawing.points.map((point: any, index: number) =>
          index === dragState.pointIndex ? newCoords : point
        )
      };

      onUpdateDrawing(dragState.drawingIndex, updatedDrawing);
    } else if (dragState.dragType === 'tool') {
      // Move entire tool
      const updatedPoints = drawing.points.map((point: any) => {
        const pixelCoords = chartToPixelCoords(point.time, point.price);
        if (!pixelCoords) return point;

        const newPixelCoords = {
          x: pixelCoords.x + deltaX,
          y: pixelCoords.y + deltaY
        };

        const newChartCoords = pixelToChartCoords(newPixelCoords.x, newPixelCoords.y);
        return newChartCoords || point;
      });

      const updatedDrawing = {
        ...drawing,
        points: updatedPoints
      };

      onUpdateDrawing(dragState.drawingIndex, updatedDrawing);
    }
  }, [dragState, drawings, pixelToChartCoords, chartToPixelCoords, onUpdateDrawing]);

  // Create draggable handles for a drawing
  const createDraggableHandles = useCallback((drawing: any, drawingIndex: number) => {
    if (!drawing.points) return [];

    const handles: any[] = [];

    drawing.points.forEach((point: any, pointIndex: number) => {
      const timeValue = typeof point.time === 'string' ? point.time : new Date(point.time).toISOString();
      
      handles.push({
        name: `Handle_${drawingIndex}_${pointIndex}`,
        type: 'scatter',
        coordinateSystem: 'cartesian2d',
        data: [[timeValue, point.price]],
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: drawing.style?.color || '#00e7b6',
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 4
        },
        z: 2000,
        silent: false,
        animation: false,
        cursor: 'move',
        drawingIndex,
        pointIndex,
        isDraggableHandle: true
      });
    });

    return handles;
  }, []);

  // Create invisible hit area for tool selection
  const createToolHitArea = useCallback((drawing: any, drawingIndex: number) => {
    if (!drawing.points || drawing.points.length < 2) return null;

    const hitAreaData = drawing.points.map((point: any) => {
      const timeValue = typeof point.time === 'string' ? point.time : new Date(point.time).toISOString();
      return [timeValue, point.price];
    });

    return {
      name: `HitArea_${drawingIndex}`,
      type: 'line',
      coordinateSystem: 'cartesian2d',
      data: hitAreaData,
      lineStyle: {
        color: 'transparent',
        width: 15 // Wide invisible line for easier selection
      },
      symbol: 'none',
      z: 1999,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex,
      isToolHitArea: true
    };
  }, []);

  return {
    dragState,
    startDrag,
    createDraggableHandles,
    createToolHitArea,
    pixelToChartCoords,
    chartToPixelCoords
  };
};

export default useDraggableTools;
