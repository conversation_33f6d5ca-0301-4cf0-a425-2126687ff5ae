import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Home,
  PieChart,
  Search,
  BarChart3,
  Globe,
  Settings,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWhopUser } from '@/contexts/WhopContext';

const BottomNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isWhopUser } = useWhopUser();

  // Don't render if it's a Whop user (they have their own bottom navigation)
  if (isWhopUser) {
    return null;
  }

  // Navigation items for regular users
  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      path: '/home'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio',
      icon: <PieChart className="w-5 h-5" />,
      path: '/portfolio-builder'
    },
    {
      id: 'agent-scanner',
      label: 'Scanner',
      icon: <Search className="w-5 h-5" />,
      path: '/agent-scanner'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtest',
      icon: <BarChart3 className="w-5 h-5" />,
      path: '/agent-backtesting'
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: <Globe className="w-5 h-5" />,
      path: '/discover'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      path: '/settings'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (item: any) => {
    navigate(item.path);
  };

  return (
    <>
      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 safe-area-inset-bottom">
        {/* Gradient overlay for seamless integration */}
        <div className="absolute inset-x-0 -top-8 h-8 bg-gradient-to-t from-[#0A0A0A] to-transparent pointer-events-none"></div>

        {/* Navigation Container */}
        <div className="bg-[#0A0A0A]/85 backdrop-blur-2xl border-t-2 border-white/[0.12] shadow-2xl">
          {/* Safe area padding for devices with home indicators */}
          <div className="pb-safe">
            <div className="flex items-center justify-around px-2 py-3 max-w-screen-xl mx-auto relative">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item)}
                className={cn(
                  "nav-button group flex flex-col items-center gap-1 px-3 py-2 rounded-xl transition-all duration-300 min-w-0 flex-shrink-0 relative overflow-hidden focus:outline-none",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.15] text-white shadow-[inset_0_2px_8px_rgba(0,0,0,0.2),inset_0_1px_0_rgba(255,255,255,0.1)]"
                    : "text-white/60 hover:text-white/90 hover:bg-white/[0.08] hover:shadow-[inset_0_1px_4px_rgba(0,0,0,0.15)]"
                )}
                title={item.label}
                aria-label={`Navigate to ${item.label}`}
              >
                {/* Icon container with enhanced styling */}
                <div className={cn(
                  "flex-shrink-0 transition-all duration-300 relative",
                  isActiveRoute(item.path)
                    ? "text-white transform scale-110"
                    : "text-white/70 group-hover:text-white/95 group-hover:scale-105"
                )}>
                  {/* Subtle glow effect for active items */}
                  {isActiveRoute(item.path) && (
                    <div className="absolute inset-0 bg-white/[0.15] rounded-lg blur-sm"></div>
                  )}
                  <div className="relative z-10">
                    {item.icon}
                  </div>
                </div>

                {/* Label with better typography */}
                <span className={cn(
                  "text-[11px] font-medium truncate text-center leading-tight transition-all duration-300",
                  isActiveRoute(item.path)
                    ? "text-white"
                    : "text-white/60 group-hover:text-white/90"
                )}
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                >
                  {item.label}
                </span>

                {/* Active indicator */}
                {isActiveRoute(item.path) && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-white rounded-full shadow-[0_0_8px_rgba(255,255,255,0.4)]"></div>
                )}

                {/* Subtle hover effect */}
                <div className="absolute inset-0 bg-gradient-to-t from-white/[0.05] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
              </button>
            ))}
            </div>
          </div>
        </div>
      </div>

      {/* Spacer to prevent content from being hidden behind the navigation */}
      <div className="h-20 pb-safe" aria-hidden="true"></div>

      {/* Custom styles for enhanced mobile support */}
      <style>{`
        /* Safe area support for devices with notches/home indicators */
        .safe-area-inset-bottom {
          padding-bottom: env(safe-area-inset-bottom);
        }

        .pb-safe {
          padding-bottom: env(safe-area-inset-bottom);
        }

        /* Enhanced touch targets for mobile */
        @media (max-width: 768px) {
          .nav-button {
            min-height: 44px;
            min-width: 44px;
          }
        }

        /* Prevent text selection on navigation items */
        .nav-button {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-tap-highlight-color: transparent;
        }
      `}</style>
    </>
  );
};

export default BottomNavigation;
