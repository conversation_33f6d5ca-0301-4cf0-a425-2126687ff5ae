import React, { useState } from 'react';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

/**
 * Test component for the Whop Intermediary Server
 * This component allows you to test the connection and functionality
 */
export const WhopIntermediaryTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test: string, result: any) => {
    setTestResults(prev => [...prev, { test, result, timestamp: new Date().toISOString() }]);
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setIsLoading(true);
    try {
      console.log(`🧪 Running test: ${testName}`);
      const result = await testFn();
      addResult(testName, result);
      console.log(`✅ Test completed: ${testName}`, result);
    } catch (error) {
      console.error(`❌ Test failed: ${testName}`, error);
      addResult(testName, { error: error instanceof Error ? error.message : 'Unknown error' });
    }
    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Whop Intermediary Server Test</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={() => runTest('Health Check', () => whopIntermediaryClient.healthCheck())}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Health Check
        </button>
        
        <button
          onClick={() => runTest('Test Connection', () => whopIntermediaryClient.testConnection())}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Connection
        </button>
        
        <button
          onClick={() => runTest('Get Current User', () => whopIntermediaryClient.getCurrentUser())}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Get Current User
        </button>

        <button
          onClick={() => runTest('Get Test User', () => whopIntermediaryClient.getTestUser())}
          disabled={isLoading}
          className="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600 disabled:opacity-50"
        >
          Get Test User
        </button>

        <button
          onClick={() => runTest('Test Proxy Mode', () => whopIntermediaryClient.testProxyMode())}
          disabled={isLoading}
          className="px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 disabled:opacity-50"
        >
          Test Proxy Mode
        </button>

        <button
          onClick={() => runTest('Debug Headers', () => whopIntermediaryClient.debugHeaders())}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
        >
          Debug Headers
        </button>

        <button
          onClick={() => runTest('Simulate Whop Context', () => whopIntermediaryClient.testWithSimulatedToken())}
          disabled={isLoading}
          className="px-4 py-2 bg-emerald-500 text-white rounded hover:bg-emerald-600 disabled:opacity-50"
        >
          Simulate Whop Context
        </button>
        
        <button
          onClick={() => runTest('Check Access', () => whopIntermediaryClient.checkUserAccess())}
          disabled={isLoading}
          className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
        >
          Check Access
        </button>
        
        <button
          onClick={() => runTest('Initialize Auth', () => whopIntermediaryClient.initializeAuth())}
          disabled={isLoading}
          className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50"
        >
          Initialize Auth
        </button>
        
        <button
          onClick={clearResults}
          disabled={isLoading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          Clear Results
        </button>
      </div>

      {isLoading && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            Running test...
          </div>
        </div>
      )}

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Test Results ({testResults.length})</h3>
        
        {testResults.length === 0 ? (
          <p className="text-gray-500 italic">No tests run yet. Click a button above to start testing.</p>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="border rounded p-3 bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{result.test}</h4>
                  <span className="text-xs text-gray-500">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                <div className="bg-white p-2 rounded border">
                  <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                    {JSON.stringify(result.result, null, 2)}
                  </pre>
                </div>
                
                {result.result.success ? (
                  <div className="mt-2 text-green-600 text-sm font-medium">✅ Success</div>
                ) : (
                  <div className="mt-2 text-red-600 text-sm font-medium">❌ Failed</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-medium text-yellow-800 mb-2">Instructions:</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <strong>Health Check</strong>: Tests if the intermediary server is running</li>
          <li>• <strong>Test Connection</strong>: Tests the Whop API connection through the server</li>
          <li>• <strong>Get Test User</strong>: Gets agent user data (works without Whop context)</li>
          <li>• <strong>Test Proxy Mode</strong>: Compares direct vs proxy communication</li>
          <li>• <strong>Get Current User</strong>: Attempts to get the current Whop user (requires Whop iframe)</li>
          <li>• <strong>Check Access</strong>: Checks user access levels (requires Whop iframe)</li>
          <li>• <strong>Initialize Auth</strong>: Full authentication flow test</li>
        </ul>
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
        <h4 className="font-medium text-blue-800 mb-2">Current Access Method:</h4>
        <div className="text-sm text-blue-700">
          <p><strong>Current URL:</strong> {window.location.href}</p>
          <p><strong>Expected for Whop context:</strong> http://localhost:3001/whop-test</p>
          <p><strong>Current (testing):</strong> http://localhost:5173/whop-test</p>
          <div className="mt-2 p-2 bg-blue-100 rounded">
            <p className="font-medium">💡 To test with Whop user tokens:</p>
            <p>Visit: <a href="http://localhost:3001/whop-test" className="underline text-blue-600">http://localhost:3001/whop-test</a></p>
            <p className="text-xs mt-1">The Whop proxy (port 3001) will add the x-whop-user-token header when you're in a real Whop iframe.</p>
          </div>
        </div>
      </div>
    </div>
  );
};
