// Client-side Whop app configuration detection and management

interface WhopAppConfig {
  appId: string;
  agentUserId: string;
  companyId: string;
  name: string;
}

// App configurations
export const WHOP_APP_CONFIGS: Record<string, WhopAppConfig> = {
  osis: {
    appId: import.meta.env.VITE_WHOP_APP_ID || '',
    agentUserId: import.meta.env.VITE_WHOP_AGENT_USER_ID || '',
    companyId: import.meta.env.VITE_WHOP_COMPANY_ID || '',
    name: 'OSIS'
  },
  trading: {
    appId: import.meta.env.VITE_TRADING_WHOP_APP_ID || '',
    agentUserId: import.meta.env.VITE_TRADING_WHOP_AGENT_USER_ID || '',
    companyId: import.meta.env.VITE_TRADING_WHOP_COMPANY_ID || '',
    name: 'Trading'
  }
};

// Detect current app context
export function detectCurrentApp(): string {
  if (typeof window === 'undefined') {
    return 'osis'; // Default for SSR
  }

  const pathname = window.location.pathname;
  const search = window.location.search;
  const hostname = window.location.hostname;
  const port = window.location.port;
  const fullUrl = window.location.href;

  console.log('🔍 App context detection:', {
    pathname,
    search,
    hostname,
    port,
    fullUrl
  });

  // Check for explicit app parameter first
  if (search.includes('app=trading')) {
    console.log('🎯 Trading app context detected (explicit parameter)');
    return 'trading';
  }

  if (search.includes('app=osis')) {
    console.log('🎯 OSIS app context detected (explicit parameter)');
    return 'osis';
  }

  // Check for experience URLs and extract app type from experience ID
  const experienceMatch = pathname.match(/\/experiences\/exp_([a-zA-Z0-9]+)/);
  if (experienceMatch) {
    const experienceId = experienceMatch[1]; // e.g., "ThljdpAF70d4Af"
    console.log('🔍 Found experience ID:', experienceId);

    // Check if this experience ID corresponds to a trading app
    // Trading experience IDs should match the pattern from trade-{id}
    // We can identify this by checking known trading experience patterns
    // For now, we'll check if the experience ID matches known trading patterns

    // You can add specific experience ID mappings here if needed
    // For the example: exp_ThljdpAF70d4Af should map to trading
    if (experienceId === 'ThljdpAF70d4Af') {
      console.log('🎯 Trading app context detected (experience ID)');
      return 'trading';
    }

    // Default experience URLs to OSIS for backward compatibility
    console.log('🎯 OSIS app context detected (experience ID default)');
    return 'osis';
  }

  // Check for trading-specific indicators FIRST
  if (
    pathname.includes('/trade') ||
    pathname.startsWith('/trade') ||
    pathname === '/trade' ||
    pathname.includes('/trading') ||
    pathname.startsWith('/trading') ||
    pathname === '/trading' ||
    fullUrl.includes('trading-') || // Trading app ID pattern like trading-abc123
    fullUrl.includes('trade-') // Trading app ID pattern like trade-ThljdpAF70d4Af
  ) {
    console.log('🎯 Trading app context detected (URL pattern)');
    return 'trading';
  }

  // Check for OSIS-specific URL patterns (specific app ID patterns only)
  if (
    fullUrl.includes('osis-') // OSIS app ID pattern like osis-cRepYjKZgFSFbL
  ) {
    console.log('🎯 OSIS app context detected (URL pattern)');
    return 'osis';
  }

  // Special case: localhost:3000 with /trading redirect
  if (port === '3000' && pathname === '/' && hostname === 'localhost') {
    // Check if we're being redirected to trading (this should only happen for trading app)
    // For now, default to OSIS unless explicitly indicated otherwise
    console.log('🎯 Localhost detected - defaulting to OSIS (use ?app=trading to override)');
    return 'osis';
  }

  // Default to OSIS app for backward compatibility
  console.log('🎯 OSIS app context detected (default)');
  return 'osis';
}

// Get current app configuration
export function getCurrentAppConfig(): WhopAppConfig {
  const appKey = detectCurrentApp();
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    console.warn(`⚠️ Unknown app key: ${appKey}, falling back to OSIS`);
    return WHOP_APP_CONFIGS.osis;
  }
  
  return config;
}

// Get app configuration by key
export function getAppConfig(appKey: string): WhopAppConfig {
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    console.warn(`⚠️ Unknown app key: ${appKey}, falling back to OSIS`);
    return WHOP_APP_CONFIGS.osis;
  }
  
  return config;
}

// Validate app configuration
export function validateAppConfig(appKey: string): boolean {
  const config = getAppConfig(appKey);
  const requiredFields = ['appId', 'agentUserId', 'companyId'];
  
  const missingFields = requiredFields.filter(field => !config[field as keyof WhopAppConfig]);
  
  if (missingFields.length > 0) {
    console.error(`❌ Missing required fields for ${config.name} app:`, missingFields);
    return false;
  }
  
  return true;
}

// Get intermediary server URL with app parameter
export function getIntermediaryUrl(endpoint: string, appKey?: string): string {
  const baseUrl = import.meta.env.VITE_WHOP_INTERMEDIARY_URL || 'https://whop-intermediary-server.vercel.app';
  const detectedApp = appKey || detectCurrentApp();
  
  const url = new URL(`/api/${endpoint}`, baseUrl);
  url.searchParams.set('app', detectedApp);
  
  return url.toString();
}

// Log configuration status
export function logAppConfigStatus(): void {
  if (typeof window === 'undefined') return;

  const currentApp = detectCurrentApp();
  const currentConfig = getCurrentAppConfig();

  console.log('🔧 Whop App Configuration:', {
    currentApp,
    appName: currentConfig.name,
    appId: currentConfig.appId,
    isValid: validateAppConfig(currentApp),
    allConfigs: Object.keys(WHOP_APP_CONFIGS).map(key => ({
      key,
      name: WHOP_APP_CONFIGS[key].name,
      isValid: validateAppConfig(key)
    }))
  });
}

// Force re-detection of app context (useful after navigation)
export function forceAppContextRedetection(): string {
  if (typeof window === 'undefined') return 'osis';

  console.log('🔄 Forcing app context re-detection...');
  const newApp = detectCurrentApp();
  logAppConfigStatus();
  return newApp;
}

// Initialize app configuration logging (development only)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  // Log on page load
  setTimeout(logAppConfigStatus, 1000);

  // Log on route changes
  let lastPathname = window.location.pathname;
  let lastSearch = window.location.search;

  setInterval(() => {
    const currentPathname = window.location.pathname;
    const currentSearch = window.location.search;

    if (currentPathname !== lastPathname || currentSearch !== lastSearch) {
      console.log('🔄 URL changed, re-detecting app context:', {
        from: { pathname: lastPathname, search: lastSearch },
        to: { pathname: currentPathname, search: currentSearch }
      });

      lastPathname = currentPathname;
      lastSearch = currentSearch;
      logAppConfigStatus();
    }
  }, 500); // Check more frequently for redirects
}
