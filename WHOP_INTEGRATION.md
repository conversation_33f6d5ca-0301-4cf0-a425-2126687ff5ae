# Whop Integration for Trade Sensei

This document outlines the Whop integration implementation for Trade Sensei, allowing users to access the platform through Whop experiences.

## Overview

The Whop integration allows Trade Sensei to be embedded as an experience within the Whop platform. Users who have access to Trade Sensei through Whop can use all the same features as regular users, but with Whop-specific authentication and access control.

## Architecture

### Key Components

1. **Whop SDK Configuration** (`src/lib/whop-sdk.ts`)
   - Configures the Whop Server SDK
   - Handles authentication and API calls
   - Validates configuration

2. **Whop Authentication** (`src/utils/whopAuth.ts`)
   - User token verification
   - Access control checking
   - Context detection

3. **Whop Context Provider** (`src/contexts/WhopContext.tsx`)
   - Global state management for Whop users
   - Authentication status tracking
   - Experience ID management

4. **Whop Components** (`src/components/whop/`)
   - `WhopExperiencePage.tsx` - Main experience wrapper
   - `WhopUserIndicator.tsx` - User status display
   - `WhopAuthGuard.tsx` - Access control component

5. **Unified Authentication** (`src/hooks/useUnifiedAuth.ts`)
   - Combines regular and Whop authentication
   - Provides single interface for auth checks

## Environment Variables

Add these to your `.env` file:

```env
# Whop Integration
VITE_WHOP_APP_ID=app_VFK6Os0L6NKH0i
WHOP_API_KEY=v3Kff2BHovdicGf9c4zAOtlx-Y7AdYyi0YsWGIF20EE
VITE_WHOP_AGENT_USER_ID=user_CsaZOGen5WUoE
VITE_WHOP_COMPANY_ID=biz_OGyv6Pz0Le35Fa
```

## Routes

### Experience Routes
- `/experiences/:experienceId` - Main Whop experience page

The experience ID is provided by Whop when users access your app through their platform.

## Authentication Flow

### For Whop Users
1. User accesses Trade Sensei through Whop
2. Whop provides user token in headers (`x-whop-user-token`)
3. Token is verified using Whop SDK
4. User access is checked for the specific experience
5. If access is granted, user can use all Trade Sensei features

### For Regular Users
1. Standard Google OAuth flow
2. Supabase authentication
3. Subscription-based access control

## Development & Testing

### Development Tools

The integration includes development tools for testing:

```javascript
// Available in browser console during development
whopDev.enableWhopDevMode()           // Enable Whop simulation
whopDev.disableWhopDevMode()          // Disable Whop simulation
whopDev.enableBasicExperience()       // Test basic experience
whopDev.enablePremiumExperience()     // Test premium experience
whopDev.enableAdminExperience()       // Test admin experience
whopDev.logWhopStatus()               // Show current status
```

### Testing Whop Integration

1. **Enable Development Mode:**
   ```javascript
   whopDev.enableWhopDevMode('exp_test_123')
   ```

2. **Navigate to Experience:**
   - Go to `/experiences/exp_test_123`
   - You should see Whop-specific UI elements

3. **Test Access Levels:**
   - Use different mock experience IDs to test various access levels
   - Check that appropriate UI elements are shown/hidden

### Mock Data

For development, the integration uses mock tokens and experience IDs:
- Mock token: `mock-whop-token-for-development`
- Mock experience IDs: `exp_basic_123`, `exp_premium_456`, `exp_admin_789`

## User Experience

### Whop Users
- See Whop user indicator in header
- Access all Trade Sensei features based on their Whop subscription
- No need for separate Google login
- Experience-specific welcome messages

### Regular Users
- Standard authentication flow
- No changes to existing functionality
- Whop-specific elements are hidden

## Key Features

### Access Control
- Experience-based access validation
- Admin/customer role differentiation
- Automatic access refresh

### UI Integration
- Whop user indicators
- Experience-specific branding
- Seamless navigation

### Logging & Debugging
- Comprehensive console logging
- Development tools for testing
- Clear distinction between user types

## API Integration

### Whop SDK Methods Used
- `whopSdk.verifyUserToken()` - Token verification
- `whopSdk.access.checkIfUserHasAccessToExperience()` - Access checking
- `whopSdk.users.getUser()` - User information retrieval

### Error Handling
- Token verification failures
- Access denied scenarios
- Network connectivity issues
- Configuration validation

## Security Considerations

1. **Token Validation:**
   - All Whop tokens are verified server-side
   - Invalid tokens result in access denial

2. **Access Control:**
   - Experience-specific access checking
   - Role-based feature access

3. **Environment Variables:**
   - API keys are kept secure
   - Client-side variables are properly prefixed

## Deployment

### Environment Setup
1. Set all required environment variables
2. Ensure Whop app is properly configured
3. Test with real Whop experience IDs

### Whop App Configuration
1. Set redirect URIs in Whop dashboard
2. Configure experience paths
3. Set up webhooks if needed

## Troubleshooting

### Common Issues

1. **"Invalid Whop configuration"**
   - Check all environment variables are set
   - Verify API key is correct

2. **"Access Denied"**
   - Verify user has access to the experience
   - Check experience ID is correct

3. **"Authentication Error"**
   - Verify user token is valid
   - Check network connectivity

### Debug Commands

```javascript
// Check configuration
whopDev.logWhopStatus()

// Test connection
await whopSdk.users.getUser({ userId: 'user_test' })

// Verify environment
console.log(import.meta.env.VITE_WHOP_APP_ID)
```

## Future Enhancements

1. **Webhook Integration:**
   - Real-time access updates
   - User subscription changes

2. **Enhanced Analytics:**
   - Whop user behavior tracking
   - Experience-specific metrics

3. **Custom Branding:**
   - Experience-specific themes
   - White-label options

## Quick Start Guide

### 1. Setup Environment
```bash
npm run setup:whop
npm run validate:whop
```

### 2. Start Development Server
```bash
npm run dev
# or for Whop proxy
npm run start:whop
```

### 3. Test Integration
```javascript
// In browser console
whopDev.enableWhopDevMode('exp_test_123')
// Navigate to /experiences/exp_test_123
```

### 4. Deploy Function (Optional)
```bash
npm run deploy:whop
```

## Support

For issues with the Whop integration:
1. Check the browser console for error messages
2. Use development tools to test functionality
3. Verify environment configuration
4. Check Whop dashboard for app status
