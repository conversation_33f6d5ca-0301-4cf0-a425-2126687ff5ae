import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Constants
const OFFICIAL_OSIS_BUSINESS_ID = 'biz_OGyv6Pz0Le35Fa';
const OFFICIAL_OSIS_HANDLE = 'tryosis';

// Helper function to check Whop access using existing API endpoints
async function checkWhopAccess(whopUserId: string, whopCompanyId: string) {
  try {
    // Use the existing whop-integration function to check access
    const response = await fetch(`${supabaseUrl}/functions/v1/whop-integration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`
      },
      body: JSON.stringify({
        action: 'check_access',
        whopUserId,
        whopCompanyId
      })
    });

    if (!response.ok) {
      console.warn('Failed to check Whop access via API, falling back to metadata check');
      return { hasAccess: true, accessLevel: 'customer' }; // Fallback
    }

    const data = await response.json();
    return data.access || { hasAccess: true, accessLevel: 'customer' };
  } catch (error) {
    console.warn('Error checking Whop access:', error);
    return { hasAccess: true, accessLevel: 'customer' }; // Fallback
  }
}

interface WhopCompetition {
  id?: string;
  name: string;
  description?: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  competition_scope: 'public' | 'whop_local' | 'whop_cross_community';
  whop_company_id?: string;
  whop_business_id?: string;
  whop_business_handle?: string;
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
}

// Create a Whop competition with permission validation
async function createWhopCompetition(userId: string, competitionData: WhopCompetition) {
  console.log('🏆 Creating Whop competition:', { userId, scope: competitionData.competition_scope });

  // Get user information
  const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);
  if (userError || !user) {
    throw new Error('User not found');
  }

  const userMetadata = user.user.user_metadata || {};
  const whopUserId = userMetadata.whop_user_id;
  const whopAccessLevel = userMetadata.whop_access_level;
  const userWhopCompanyId = userMetadata.whop_company_id;
  const userWhopBusinessId = userMetadata.whop_business_id;

  // Validate Whop permissions using database functions
  if (competitionData.competition_scope !== 'public') {
    if (!whopUserId) {
      throw new Error('Only Whop users can create Whop competitions');
    }

    // Use database function to validate permissions
    const { data: validationResult, error: validationError } = await supabase
      .rpc('validate_whop_competition_permissions', {
        p_creator_id: userId,
        p_whop_company_id: userWhopCompanyId,
        p_whop_business_id: userWhopBusinessId,
        p_competition_scope: competitionData.competition_scope,
        p_is_cross_community: competitionData.is_cross_community || false
      });

    if (validationError) {
      console.error('❌ Validation error:', validationError);
      throw new Error('Permission validation failed');
    }

    if (validationResult && validationResult.length > 0 && !validationResult[0].is_valid) {
      throw new Error(validationResult[0].error_message);
    }

    // Set Whop fields
    competitionData.whop_company_id = userWhopCompanyId;
    competitionData.whop_business_id = userWhopBusinessId;
    competitionData.whop_business_handle = userMetadata.whop_business_handle;
  }

  // Create the competition (the trigger will validate permissions again)
  const { data, error } = await supabase
    .from('paper_trading_competitions')
    .insert({
      ...competitionData,
      creator_id: userId,
      status: 'draft'
    })
    .select()
    .single();

  if (error) {
    console.error('❌ Error creating competition:', error);
    throw error;
  }

  console.log('✅ Competition created successfully:', data.id);
  return data;
}

// Get competitions with Whop filtering (using RLS policies)
async function getWhopCompetitions(userId?: string) {
  console.log('📋 Fetching Whop competitions for user:', userId);

  // The RLS policies will automatically filter competitions based on user permissions
  // We just need to query all competitions and let the database handle the filtering
  const { data, error } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .in('status', ['open', 'active', 'completed'])
    .order('created_at', { ascending: false });

  if (error) {
    console.error('❌ Error fetching competitions:', error);
    throw error;
  }

  console.log(`✅ Returning ${data.length} competitions`);
  return data;
}

// Check if user can join a specific competition
async function checkJoinPermission(userId: string, competitionId: string) {
  console.log('🔍 Checking join permission:', { userId, competitionId });

  // Get competition details using RLS (will return null if user can't see it)
  const { data: competition, error: compError } = await supabase
    .from('paper_trading_competitions')
    .select('*')
    .eq('id', competitionId)
    .single();

  if (compError || !competition) {
    return { canJoin: false, reason: 'Competition not found or access denied' };
  }

  // If user can see the competition via RLS, they can join it
  // Additional checks can be added here for specific business rules

  // Check if competition is open for registration
  if (competition.status !== 'open') {
    return { canJoin: false, reason: 'Competition is not open for registration' };
  }

  // Check if registration period is active (if set)
  if (competition.registration_end) {
    const now = new Date();
    const registrationEnd = new Date(competition.registration_end);
    if (now > registrationEnd) {
      return { canJoin: false, reason: 'Registration period has ended' };
    }
  }

  // Check if competition is full
  if (competition.max_participants) {
    const { count } = await supabase
      .from('competition_participants')
      .select('*', { count: 'exact', head: true })
      .eq('competition_id', competitionId)
      .eq('is_active', true);

    if (count && count >= competition.max_participants) {
      return { canJoin: false, reason: 'Competition is full' };
    }
  }

  return { canJoin: true };
}

// Join a Whop competition with permission validation
async function joinWhopCompetition(userId: string, competitionId: string) {
  console.log('🎯 Joining Whop competition:', { userId, competitionId });

  // Check join permission first
  const permission = await checkJoinPermission(userId, competitionId);
  if (!permission.canJoin) {
    throw new Error(permission.reason || 'Cannot join this competition');
  }

  // Get user's paper trading account
  const { data: account, error: accountError } = await supabase
    .from('paper_trading_accounts')
    .select('id')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single();

  if (accountError || !account) {
    throw new Error('No active paper trading account found');
  }

  // Join the competition
  const { data, error } = await supabase
    .from('competition_participants')
    .insert({
      competition_id: competitionId,
      user_id: userId,
      account_id: account.id
    })
    .select()
    .single();

  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      throw new Error('You are already participating in this competition');
    }
    throw error;
  }

  console.log('✅ Successfully joined competition');
  return data;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get user from JWT token
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      throw new Error('Invalid authentication token');
    }

    const { action, ...params } = await req.json();

    let result;
    switch (action) {
      case 'create_competition':
        result = await createWhopCompetition(user.id, params as WhopCompetition);
        break;

      case 'get_whop_competitions':
        result = await getWhopCompetitions(user.id);
        break;

      case 'check_join_permission':
        result = await checkJoinPermission(user.id, params.competition_id);
        break;

      case 'join_competition':
        result = await joinWhopCompetition(user.id, params.competition_id);
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Error in whop-competition-management:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error' 
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
