import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { apiMiddleware } from "./vite-api-middleware.js";
import { whopProxyMiddleware } from "./vite-whop-middleware.js";
// Remove the component tagger import since we're not using it
// import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';

  return {
    server: {
      host: "::",
      port: 8080
    },
    plugins: [
      react({
        // Enable React Fast Refresh for better development experience
        fastRefresh: true,
      }),
      apiMiddleware(),
      whopProxyMiddleware()
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      // Explicitly make Vite replace env variables for Whop
      'process.env.WHOP_CLIENT_ID': JSON.stringify(env.WHOP_CLIENT_ID),
      'process.env.WHOP_CLIENT_SECRET': JSON.stringify(env.WHOP_CLIENT_SECRET),
      'process.env.WHOP_REDIRECT_URI': JSON.stringify(env.WHOP_REDIRECT_URI),
      'process.env.WHOP_API_KEY': JSON.stringify(env.WHOP_API_KEY),
      'process.env.VITE_WHOP_APP_ID': JSON.stringify(env.VITE_WHOP_APP_ID),
      'process.env.VITE_WHOP_AGENT_USER_ID': JSON.stringify(env.VITE_WHOP_AGENT_USER_ID),
      'process.env.VITE_WHOP_COMPANY_ID': JSON.stringify(env.VITE_WHOP_COMPANY_ID)
    },
    build: {
      // Optimize build for performance
      target: 'es2020',
      minify: 'esbuild',
      cssMinify: true,
      sourcemap: !isProduction,

      // Enable code splitting and chunk optimization
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            // Vendor chunks
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-toast'],
            'chart-vendor': ['echarts', 'echarts-for-react', 'lightweight-charts', 'recharts'],
            'query-vendor': ['@tanstack/react-query'],
            'supabase-vendor': ['@supabase/supabase-js'],
            'utils-vendor': ['axios', 'date-fns', 'clsx', 'tailwind-merge'],

            // Feature-based chunks
            'auth': ['src/contexts/AuthContext.tsx', 'src/contexts/WhopContext.tsx'],
            'pages-core': ['src/pages/Home.tsx', 'src/pages/Settings.tsx'],
            'pages-trading': ['src/pages/Trades.tsx', 'src/pages/PortfolioManager.tsx'],
            'pages-agents': ['src/pages/AgentBuilder.tsx', 'src/pages/AgentManagement.tsx'],
          },

          // Optimize chunk file names for caching
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '') : 'chunk';
            return `assets/js/[name]-[hash].js`;
          },
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        },
      },

      // Optimize chunk size warnings
      chunkSizeWarningLimit: 1000,
    },

    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        '@supabase/supabase-js',
        'axios',
        'date-fns',
        'clsx',
        'tailwind-merge',
        'framer-motion',
        'lucide-react'
      ],
      exclude: [
        // Exclude large dependencies that should be loaded on demand
        'echarts',
        'lightweight-charts',
        'html2canvas',
        'dom-to-image'
      ]
    },

    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.ts'],
      css: false,
    }
  };
});
