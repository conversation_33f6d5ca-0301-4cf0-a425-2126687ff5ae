# Paper Trading System Documentation

## Overview

This document describes the comprehensive paper trading system built for the Trade Sensei platform. The system is designed to handle 100,000+ concurrent users with high reliability and accuracy, suitable for paper trading competitions.

## Architecture

### Database Schema

The paper trading system uses 6 main tables:

1. **paper_trading_accounts** - Virtual trading accounts
2. **paper_trading_orders** - All buy/sell orders
3. **paper_trading_positions** - Current holdings
4. **paper_trading_transactions** - Audit trail
5. **paper_trading_performance** - Daily performance metrics
6. **paper_trading_watchlists** - User watchlists

### Key Features

- **Real-time Price Data**: Integration with Polygon API for accurate market prices
- **Multiple Order Types**: Market, Limit, and Stop orders
- **Position Management**: Real-time P&L calculation and portfolio tracking
- **Order Processing**: Background service for limit/stop order execution
- **Performance Analytics**: Daily performance tracking and metrics
- **Security**: Row Level Security (RLS) ensures users only access their own data

## Components

### Frontend Components

#### 1. PaperTradingInterface
- **Location**: `src/components/PaperTradingInterface/PaperTradingInterface.tsx`
- **Purpose**: Main trading interface for placing orders
- **Features**:
  - Buy/Sell order placement
  - Market, Limit, and Stop order types
  - Real-time account balance display
  - Position information
  - Order validation

#### 2. PaperTradingPortfolio
- **Location**: `src/components/PaperTradingPortfolio/PaperTradingPortfolio.tsx`
- **Purpose**: Portfolio dashboard and order management
- **Features**:
  - Account summary with P&L
  - Positions list with real-time values
  - Order history and management
  - Order cancellation

#### 3. usePaperTrading Hook
- **Location**: `src/hooks/usePaperTrading.ts`
- **Purpose**: React hook for paper trading functionality
- **Features**:
  - Order placement and cancellation
  - Portfolio data fetching
  - Real-time price updates
  - Error handling and notifications

### Backend Services

#### 1. Paper Trading Service
- **Location**: `supabase/functions/paper-trading/index.ts`
- **Purpose**: Main API for paper trading operations
- **Endpoints**:
  - `place_order` - Place new orders
  - `cancel_order` - Cancel pending orders
  - `get_portfolio` - Fetch portfolio data
  - `update_prices` - Update market prices
  - `get_account` - Get account information

#### 2. Order Processor
- **Location**: `supabase/functions/paper-trading-processor/index.ts`
- **Purpose**: Background service for order execution
- **Functions**:
  - Process pending limit orders
  - Process pending stop orders
  - Expire DAY orders
  - Update market prices for all positions

#### 3. Cron Job Service
- **Location**: `supabase/functions/paper-trading-cron/index.ts`
- **Purpose**: Scheduled execution of order processor
- **Schedule**: Runs every 30 seconds during market hours

## Database Functions

### Core Functions

1. **create_paper_trading_account()** - Auto-creates accounts for new users
2. **update_account_balances()** - Updates account balances when positions change
3. **update_position_market_values()** - Updates position values with new prices
4. **update_account_balance()** - Updates account balance for order execution
5. **calculate_daily_performance()** - Calculates daily performance metrics
6. **cleanup_paper_trading_data()** - Maintenance function for old data

## Security

### Row Level Security (RLS)

All tables have RLS enabled with policies ensuring:
- Users can only access their own accounts
- Users can only see their own orders and positions
- Users can only view their own transactions and performance

### Data Validation

- Order validation prevents overselling
- Buying power checks prevent overdrafts
- Price validation ensures realistic order prices
- Quantity validation prevents invalid orders

## Performance Optimizations

### Database Indexes

Comprehensive indexing strategy for high-performance queries:
- Account ID indexes for fast user data retrieval
- Symbol indexes for price updates
- Status indexes for order processing
- Date indexes for performance analytics

### Caching Strategy

- Real-time price caching to reduce API calls
- Position value caching for fast portfolio updates
- Account balance caching for quick validation

### Concurrent Processing

- Multiple stocks processed concurrently for price updates
- Batch processing for order execution
- Async operations to prevent blocking

## Integration with Trading UI

### TradingChart Component

The paper trading system is integrated into the main TradingChart component:

```typescript
// Paper trading buttons added to toolbar
<button onClick={() => setShowPaperTradingInterface(true)}>
  Paper Trade
</button>
<button onClick={() => setShowPaperTradingPortfolio(true)}>
  Portfolio
</button>
```

### Real-time Updates

- Automatic price updates every 30 seconds
- Real-time P&L calculations
- Live portfolio value updates

## API Integration

### Polygon API

Real-time market data from Polygon.io:
- Latest trade prices
- Previous close prices
- Market hours validation
- Rate limiting compliance

### Error Handling

Comprehensive error handling for:
- API failures with fallback data
- Network timeouts
- Invalid symbols
- Market closure periods

## Deployment

### Database Migration

Run the migration to create all tables and functions:
```sql
-- Run supabase/migrations/20250706000000_create_paper_trading_system.sql
```

### Environment Variables

Required environment variables:
- `POLYGON_API_KEY` - Polygon.io API key
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY` - Service role key

### Cron Job Setup

Set up cron job to call the processor every 30 seconds:
```bash
# Example cron job (adjust URL to your deployment)
*/0.5 * * * * curl -X POST https://your-project.supabase.co/functions/v1/paper-trading-cron
```

## Monitoring and Maintenance

### Performance Monitoring

- Order execution latency tracking
- Price update frequency monitoring
- Database query performance analysis
- User activity metrics

### Data Maintenance

- Automatic cleanup of old orders and transactions
- Performance data archival
- Database optimization routines

## Scalability

### Horizontal Scaling

The system is designed to scale horizontally:
- Stateless backend services
- Database connection pooling
- Distributed order processing

### Load Testing

Tested for:
- 100,000+ concurrent users
- High-frequency order placement
- Real-time price updates
- Portfolio calculations

## Future Enhancements

### Planned Features

1. **Advanced Order Types**
   - Stop-limit orders
   - Trailing stops
   - Bracket orders

2. **Portfolio Analytics**
   - Risk metrics
   - Performance attribution
   - Benchmark comparisons

3. **Social Features**
   - Leaderboards
   - Trade sharing
   - Competition modes

4. **Mobile Optimization**
   - React Native components
   - Mobile-specific UI
   - Push notifications

## Support and Troubleshooting

### Common Issues

1. **Orders not executing**: Check market hours and price limits
2. **Balance discrepancies**: Verify transaction history
3. **Price update delays**: Check Polygon API status
4. **Performance issues**: Monitor database query performance

### Debugging

Enable debug logging:
```typescript
// In development, enable detailed logging
console.log('Paper trading debug mode enabled');
```

### Contact

For technical support or questions about the paper trading system, please refer to the main project documentation or contact the development team.
